#ifndef MATRIX_STACK_H
#define MATRIX_STACK_H

#include "math_utils.h"
#include <stack>

class MatrixStack {
private:
    std::stack<Mat4> stack;
    Mat4 currentMatrix;
    
public:
    MatrixStack() {
        currentMatrix = Mat4::identity();
        stack.push(currentMatrix);
    }
    
    // Push current matrix onto stack
    void push() {
        stack.push(currentMatrix);
    }
    
    // Pop matrix from stack and make it current
    void pop() {
        if (!stack.empty()) {
            currentMatrix = stack.top();
            stack.pop();
        }
    }
    
    // Get current transformation matrix
    const Mat4& current() const {
        return currentMatrix;
    }
    
    // Load identity matrix
    void loadIdentity() {
        currentMatrix = Mat4::identity();
    }
    
    // Apply transformations (multiply current matrix)
    void translate(const Vec3& translation) {
        currentMatrix = currentMatrix * Mat4::translate(translation);
    }
    
    void scale(const Vec3& scale) {
        currentMatrix = currentMatrix * Mat4::scale(scale);
    }
    
    void rotateX(float angle) {
        currentMatrix = currentMatrix * Mat4::rotateX(angle);
    }
    
    void rotateY(float angle) {
        currentMatrix = currentMatrix * Mat4::rotateY(angle);
    }
    
    void rotateZ(float angle) {
        currentMatrix = currentMatrix * Mat4::rotateZ(angle);
    }
    
    // Multiply by arbitrary matrix
    void multiply(const Mat4& matrix) {
        currentMatrix = currentMatrix * matrix;
    }
    
    // Load specific matrix (replace current)
    void load(const Mat4& matrix) {
        currentMatrix = matrix;
    }
    
    // Get stack size (for debugging)
    size_t size() const {
        return stack.size();
    }
};

#endif // MATRIX_STACK_H
