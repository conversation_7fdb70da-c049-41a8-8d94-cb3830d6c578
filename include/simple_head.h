#ifndef SIMPLE_HEAD_H
#define SIMPLE_HEAD_H

#include "math_utils.h"
#include "matrix_stack.h"
#include "shader.h"
#include "cube_renderer.h"

// Simple head model that rotates
enum class AnimationState {
    IDLE,
    ROTATING
};

class SimpleHead {
private:
    // Animation parameters
    float animationTime;
    AnimationState currentAnimation;
    
    void drawCube(const Vec3& position, const Vec3& scale, const Vec3& color,
                  MatrixStack& matrixStack, const Shader& shader, 
                  const CubeRenderer& cubeRenderer, const Mat4& projectionMatrix, 
                  const Mat4& viewMatrix) {
        
        matrixStack.push();
        matrixStack.translate(position);
        matrixStack.scale(scale);
        
        // Calculate matrices
        Mat4 modelMatrix = matrixStack.current();
        Mat4 mvpMatrix = projectionMatrix * viewMatrix * modelMatrix;
        Mat4 normalMatrix = modelMatrix;
        
        // Set uniforms
        shader.setMat4("mvpMatrix", mvpMatrix);
        shader.setMat4("modelMatrix", modelMatrix);
        shader.setMat4("normalMatrix", normalMatrix);
        shader.setVec3("objectColor", color);
        
        // Render the cube
        cubeRenderer.render();
        
        matrixStack.pop();
    }

public:
    SimpleHead() : animationTime(0.0f), currentAnimation(AnimationState::IDLE) {}
    
    void setAnimation(AnimationState animation) {
        currentAnimation = animation;
        animationTime = 0.0f; // Reset animation time
    }
    
    void update(float deltaTime) {
        animationTime += deltaTime;
    }
    
    void render(MatrixStack& matrixStack, const Shader& shader, const CubeRenderer& cubeRenderer,
               const Mat4& projectionMatrix, const Mat4& viewMatrix) {
        
        // Save matrix state
        matrixStack.push();
        
        // Apply rotation
        if (currentAnimation == AnimationState::ROTATING) {
            matrixStack.rotateY(animationTime); // Fast rotation around Y axis
        }
        // When idle, no additional rotation - let the isometric camera show the cube properly
        
        // Draw ONLY the main head cube - this is the 1x1x1 cube at origin as required
        // This eliminates artifacts from overlapping geometry
        drawCube(Vec3(0.0f, 0.0f, 0.0f), Vec3(1.0f, 1.0f, 1.0f), Vec3(0.8f, 0.6f, 0.4f), // orange/brown color for visibility
                matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        
        // Restore matrix state
        matrixStack.pop();
    }
};

#endif // SIMPLE_HEAD_H
