#ifndef CUBE_RENDERER_H
#define CUBE_RENDERER_H

#include <SDL2/SDL_opengl.h>
#include <vector>

class CubeRenderer {
private:
    <PERSON><PERSON><PERSON><PERSON>, VBO, EBO;
    
    // Cube vertices (position + normal)
    std::vector<float> vertices = {
        // Front face
        -0.5f, -0.5f,  0.5f,  0.0f,  0.0f,  1.0f,  // Bottom-left
         0.5f, -0.5f,  0.5f,  0.0f,  0.0f,  1.0f,  // Bottom-right
         0.5f,  0.5f,  0.5f,  0.0f,  0.0f,  1.0f,  // Top-right
        -0.5f,  0.5f,  0.5f,  0.0f,  0.0f,  1.0f,  // Top-left
        
        // Back face
        -0.5f, -0.5f, -0.5f,  0.0f,  0.0f, -1.0f,  // Bottom-left
         0.5f, -0.5f, -0.5f,  0.0f,  0.0f, -1.0f,  // Bottom-right
         0.5f,  0.5f, -0.5f,  0.0f,  0.0f, -1.0f,  // Top-right
        -0.5f,  0.5f, -0.5f,  0.0f,  0.0f, -1.0f,  // Top-left
        
        // Left face
        -0.5f, -0.5f, -0.5f, -1.0f,  0.0f,  0.0f,  // Bottom-left
        -0.5f, -0.5f,  0.5f, -1.0f,  0.0f,  0.0f,  // Bottom-right
        -0.5f,  0.5f,  0.5f, -1.0f,  0.0f,  0.0f,  // Top-right
        -0.5f,  0.5f, -0.5f, -1.0f,  0.0f,  0.0f,  // Top-left
        
        // Right face
         0.5f, -0.5f, -0.5f,  1.0f,  0.0f,  0.0f,  // Bottom-left
         0.5f, -0.5f,  0.5f,  1.0f,  0.0f,  0.0f,  // Bottom-right
         0.5f,  0.5f,  0.5f,  1.0f,  0.0f,  0.0f,  // Top-right
         0.5f,  0.5f, -0.5f,  1.0f,  0.0f,  0.0f,  // Top-left
        
        // Top face
        -0.5f,  0.5f, -0.5f,  0.0f,  1.0f,  0.0f,  // Bottom-left
         0.5f,  0.5f, -0.5f,  0.0f,  1.0f,  0.0f,  // Bottom-right
         0.5f,  0.5f,  0.5f,  0.0f,  1.0f,  0.0f,  // Top-right
        -0.5f,  0.5f,  0.5f,  0.0f,  1.0f,  0.0f,  // Top-left
        
        // Bottom face
        -0.5f, -0.5f, -0.5f,  0.0f, -1.0f,  0.0f,  // Bottom-left
         0.5f, -0.5f, -0.5f,  0.0f, -1.0f,  0.0f,  // Bottom-right
         0.5f, -0.5f,  0.5f,  0.0f, -1.0f,  0.0f,  // Top-right
        -0.5f, -0.5f,  0.5f,  0.0f, -1.0f,  0.0f   // Top-left
    };
    
    // Cube indices
    std::vector<unsigned int> indices = {
        // Front face
        0, 1, 2, 2, 3, 0,
        // Back face
        4, 5, 6, 6, 7, 4,
        // Left face
        8, 9, 10, 10, 11, 8,
        // Right face
        12, 13, 14, 14, 15, 12,
        // Top face
        16, 17, 18, 18, 19, 16,
        // Bottom face
        20, 21, 22, 22, 23, 20
    };
    
public:
    CubeRenderer() : VAO(0), VBO(0), EBO(0) {}
    
    ~CubeRenderer() {
        cleanup();
    }
    
    bool initialize() {
        // Generate buffers
        glGenVertexArrays(1, &VAO);
        glGenBuffers(1, &VBO);
        glGenBuffers(1, &EBO);
        
        // Bind VAO
        glBindVertexArray(VAO);
        
        // Bind and fill VBO
        glBindBuffer(GL_ARRAY_BUFFER, VBO);
        glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(float), vertices.data(), GL_STATIC_DRAW);
        
        // Bind and fill EBO
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, EBO);
        glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(unsigned int), indices.data(), GL_STATIC_DRAW);
        
        // Set vertex attributes
        // Position attribute (location = 0)
        glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), static_cast<void*>(0));
        glEnableVertexAttribArray(0);
        
        // Normal attribute (location = 1)
        glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), reinterpret_cast<void*>(3 * sizeof(float)));
        glEnableVertexAttribArray(1);
        
        // Unbind VAO
        glBindVertexArray(0);
        
        // Check for OpenGL errors
        GLenum error = glGetError();
        if (error != GL_NO_ERROR) {
            std::cerr << "OpenGL error in CubeRenderer::initialize(): " << error << std::endl;
            return false;
        }
        
        return true;
    }
    
    void render() const {
        if (VAO != 0) {
            glBindVertexArray(VAO);
            glDrawElements(GL_TRIANGLES, static_cast<GLsizei>(indices.size()), GL_UNSIGNED_INT, 0);
            glBindVertexArray(0);
        }
    }
    
    void cleanup() {
        if (VAO != 0) {
            glDeleteVertexArrays(1, &VAO);
            VAO = 0;
        }
        if (VBO != 0) {
            glDeleteBuffers(1, &VBO);
            VBO = 0;
        }
        if (EBO != 0) {
            glDeleteBuffers(1, &EBO);
            EBO = 0;
        }
    }
};

#endif // CUBE_RENDERER_H
