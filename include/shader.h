#ifndef SHADER_H
#define SHADER_H

#define GL_GLEXT_PROTOTYPES
#include <SDL2/SDL_opengl.h>
#include <string>
#include <fstream>
#include <sstream>
#include <iostream>
#include "math_utils.h"

class Shader {
private:
    GLuint programID;
    
    std::string readFile(const std::string& filePath) {
        std::ifstream file(filePath);
        if (!file.is_open()) {
            std::cerr << "Failed to open shader file: " << filePath << std::endl;
            return "";
        }
        
        std::stringstream buffer;
        buffer << file.rdbuf();
        return buffer.str();
    }
    
    GLuint compileShader(const std::string& source, GLenum shaderType) {
        GLuint shader = glCreateShader(shaderType);
        const char* sourceCStr = source.c_str();
        glShaderSource(shader, 1, &sourceCStr, nullptr);
        glCompileShader(shader);
        
        // Check compilation status
        GLint success;
        glGetShaderiv(shader, GL_COMPILE_STATUS, &success);
        if (!success) {
            GLchar infoLog[512];
            glGetShaderInfoLog(shader, 512, nullptr, infoLog);
            std::cerr << "Shader compilation failed: " << infoLog << std::endl;
            glDeleteShader(shader);
            return 0;
        }
        
        return shader;
    }
    
public:
    Shader() : programID(0) {}
    
    ~Shader() {
        if (programID != 0) {
            glDeleteProgram(programID);
        }
    }
    
    bool loadFromFiles(const std::string& vertexPath, const std::string& fragmentPath) {
        std::string vertexSource = readFile(vertexPath);
        std::string fragmentSource = readFile(fragmentPath);
        
        if (vertexSource.empty() || fragmentSource.empty()) {
            return false;
        }
        
        return loadFromSource(vertexSource, fragmentSource);
    }
    
    bool loadFromSource(const std::string& vertexSource, const std::string& fragmentSource) {
        GLuint vertexShader = compileShader(vertexSource, GL_VERTEX_SHADER);
        GLuint fragmentShader = compileShader(fragmentSource, GL_FRAGMENT_SHADER);
        
        if (vertexShader == 0 || fragmentShader == 0) {
            if (vertexShader != 0) glDeleteShader(vertexShader);
            if (fragmentShader != 0) glDeleteShader(fragmentShader);
            return false;
        }
        
        // Create program and link shaders
        programID = glCreateProgram();
        glAttachShader(programID, vertexShader);
        glAttachShader(programID, fragmentShader);
        glLinkProgram(programID);
        
        // Check linking status
        GLint success;
        glGetProgramiv(programID, GL_LINK_STATUS, &success);
        if (!success) {
            GLchar infoLog[512];
            glGetProgramInfoLog(programID, 512, nullptr, infoLog);
            std::cerr << "Shader program linking failed: " << infoLog << std::endl;
            glDeleteProgram(programID);
            programID = 0;
        }
        
        // Clean up shaders (they're linked into the program now)
        glDeleteShader(vertexShader);
        glDeleteShader(fragmentShader);
        
        return success != 0;
    }
    
    void use() const {
        if (programID != 0) {
            glUseProgram(programID);
        }
    }
    
    GLuint getID() const {
        return programID;
    }
    
    // Uniform setters
    void setMat4(const std::string& name, const Mat4& matrix) const {
        GLint location = glGetUniformLocation(programID, name.c_str());
        if (location != -1) {
            glUniformMatrix4fv(location, 1, GL_FALSE, matrix.data());
        }
    }
    
    void setVec3(const std::string& name, const Vec3& vector) const {
        GLint location = glGetUniformLocation(programID, name.c_str());
        if (location != -1) {
            glUniform3f(location, vector.x, vector.y, vector.z);
        }
    }
    
    void setFloat(const std::string& name, float value) const {
        GLint location = glGetUniformLocation(programID, name.c_str());
        if (location != -1) {
            glUniform1f(location, value);
        }
    }
    
    void setInt(const std::string& name, int value) const {
        GLint location = glGetUniformLocation(programID, name.c_str());
        if (location != -1) {
            glUniform1i(location, value);
        }
    }
};

#endif // SHADER_H
