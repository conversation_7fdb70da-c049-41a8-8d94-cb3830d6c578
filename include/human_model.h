#ifndef HUMAN_MODEL_H
#define HUMAN_MODEL_H

#include "math_utils.h"
#include "matrix_stack.h"
#include "shader.h"
#include "cube_renderer.h"
#include <memory>

// Body part types
enum class BodyPart {
    TORSO,
    HEAD,
    LEFT_UPPER_ARM,
    LEFT_FOREARM,
    RIGHT_UPPER_ARM,
    RIGHT_FOREARM,
    LEFT_THIGH,
    LEFT_LOWER_LEG,
    RIGHT_THIGH,
    RIGHT_LOWER_LEG
};

// Animation states
enum class AnimationState {
    IDLE,
    WALKING,
    JUMPING
};

class HumanModel {
private:
    // Body part dimensions and colors
    struct BodyPartInfo {
        Vec3 scale;
        Vec3 color;
        Vec3 offset;  // Offset from parent's origin
    };
    
    // Animation parameters
    float animationTime;
    AnimationState currentAnimation;
    
    // Body part configurations
    BodyPartInfo bodyParts[10];
    
    void initializeBodyParts() {
        // Torso
        bodyParts[static_cast<int>(BodyPart::TORSO)] = {
            Vec3(1.0f, 1.5f, 0.5f),  // scale - wider and taller
            Vec3(0.2f, 0.4f, 0.8f),  // color (blue shirt)
            Vec3(0.0f, 0.0f, 0.0f)   // offset
        };

        // Head
        bodyParts[static_cast<int>(BodyPart::HEAD)] = {
            Vec3(0.7f, 0.7f, 0.7f),  // scale
            Vec3(0.9f, 0.8f, 0.7f),  // color (skin tone)
            Vec3(0.0f, 1.4f, 0.0f)   // offset (above torso, adjusted for new torso height)
        };
        
        // Left upper arm
        bodyParts[static_cast<int>(BodyPart::LEFT_UPPER_ARM)] = {
            Vec3(0.3f, 1.0f, 0.3f),  // scale
            Vec3(0.9f, 0.8f, 0.7f),  // color (skin tone)
            Vec3(-0.8f, 0.5f, 0.0f)  // offset (left side of torso, further out)
        };

        // Left forearm
        bodyParts[static_cast<int>(BodyPart::LEFT_FOREARM)] = {
            Vec3(0.25f, 0.8f, 0.25f), // scale
            Vec3(0.9f, 0.8f, 0.7f),   // color (skin tone)
            Vec3(0.0f, -0.9f, 0.0f)   // offset (below upper arm)
        };

        // Right upper arm
        bodyParts[static_cast<int>(BodyPart::RIGHT_UPPER_ARM)] = {
            Vec3(0.3f, 1.0f, 0.3f),  // scale
            Vec3(0.9f, 0.8f, 0.7f),  // color (skin tone)
            Vec3(0.8f, 0.5f, 0.0f)   // offset (right side of torso, further out)
        };

        // Right forearm
        bodyParts[static_cast<int>(BodyPart::RIGHT_FOREARM)] = {
            Vec3(0.25f, 0.8f, 0.25f), // scale
            Vec3(0.9f, 0.8f, 0.7f),   // color (skin tone)
            Vec3(0.0f, -0.9f, 0.0f)   // offset (below upper arm)
        };
        
        // Left thigh
        bodyParts[static_cast<int>(BodyPart::LEFT_THIGH)] = {
            Vec3(0.4f, 1.2f, 0.4f),   // scale - longer legs
            Vec3(0.2f, 0.2f, 0.8f),   // color (blue pants)
            Vec3(-0.3f, -1.2f, 0.0f)  // offset (below torso, left, adjusted for new torso)
        };

        // Left lower leg
        bodyParts[static_cast<int>(BodyPart::LEFT_LOWER_LEG)] = {
            Vec3(0.3f, 1.0f, 0.3f),   // scale
            Vec3(0.9f, 0.8f, 0.7f),   // color (skin tone)
            Vec3(0.0f, -1.1f, 0.0f)   // offset (below thigh)
        };

        // Right thigh
        bodyParts[static_cast<int>(BodyPart::RIGHT_THIGH)] = {
            Vec3(0.4f, 1.2f, 0.4f),   // scale - longer legs
            Vec3(0.2f, 0.2f, 0.8f),   // color (blue pants)
            Vec3(0.3f, -1.2f, 0.0f)   // offset (below torso, right, adjusted for new torso)
        };

        // Right lower leg
        bodyParts[static_cast<int>(BodyPart::RIGHT_LOWER_LEG)] = {
            Vec3(0.3f, 1.0f, 0.3f),   // scale
            Vec3(0.9f, 0.8f, 0.7f),   // color (skin tone)
            Vec3(0.0f, -1.1f, 0.0f)   // offset (below thigh)
        };
    }
    
    void drawBodyPart(BodyPart part, MatrixStack& matrixStack, const Shader& shader, 
                     const CubeRenderer& cubeRenderer, const Mat4& projectionMatrix, 
                     const Mat4& viewMatrix) {
        const BodyPartInfo& info = bodyParts[static_cast<int>(part)];
        
        // Apply transformations
        matrixStack.translate(info.offset);
        matrixStack.scale(info.scale);
        
        // Calculate matrices
        Mat4 modelMatrix = matrixStack.current();
        Mat4 mvpMatrix = projectionMatrix * viewMatrix * modelMatrix;
        Mat4 normalMatrix = modelMatrix;
        
        // Set uniforms
        shader.setMat4("mvpMatrix", mvpMatrix);
        shader.setMat4("modelMatrix", modelMatrix);
        shader.setMat4("normalMatrix", normalMatrix);
        shader.setVec3("objectColor", info.color);
        
        // Render the cube
        cubeRenderer.render();
    }
    
    float getWalkAnimation(float time, float frequency = 2.0f) {
        return std::sin(time * frequency);
    }
    
    float getJumpAnimation(float time) {
        // Simple bounce animation
        float t = std::fmod(time, 2.0f); // 2 second cycle
        if (t < 1.0f) {
            return t * (2.0f - t); // Parabolic up
        } else {
            return 0.0f; // On ground
        }
    }

public:
    HumanModel() : animationTime(0.0f), currentAnimation(AnimationState::IDLE) {
        initializeBodyParts();
    }
    
    void setAnimation(AnimationState animation) {
        currentAnimation = animation;
        animationTime = 0.0f; // Reset animation time
    }
    
    void update(float deltaTime) {
        animationTime += deltaTime;
    }
    
    void render(MatrixStack& matrixStack, const Shader& shader, const CubeRenderer& cubeRenderer,
               const Mat4& projectionMatrix, const Mat4& viewMatrix) {
        
        // Save the current matrix state
        matrixStack.push();

        // Position the human model above ground level
        matrixStack.translate(Vec3(0.0f, 1.5f, 0.0f));

        // Apply global transformations based on animation
        if (currentAnimation == AnimationState::JUMPING) {
            float jumpHeight = getJumpAnimation(animationTime) * 2.0f;
            matrixStack.translate(Vec3(0.0f, jumpHeight, 0.0f));
        }
        
        // Draw torso (root of hierarchy)
        matrixStack.push();
        drawBodyPart(BodyPart::TORSO, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        
        // Draw head (child of torso)
        matrixStack.push();
        drawBodyPart(BodyPart::HEAD, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        matrixStack.pop();
        
        // Draw left arm hierarchy
        matrixStack.push();
        if (currentAnimation == AnimationState::WALKING) {
            float armSwing = getWalkAnimation(animationTime) * 0.5f;
            matrixStack.rotateX(armSwing);
        }
        drawBodyPart(BodyPart::LEFT_UPPER_ARM, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        
        // Left forearm (child of left upper arm)
        matrixStack.push();
        if (currentAnimation == AnimationState::WALKING) {
            float forearmBend = std::abs(getWalkAnimation(animationTime * 2.0f)) * 0.3f;
            matrixStack.rotateX(-forearmBend);
        }
        drawBodyPart(BodyPart::LEFT_FOREARM, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        matrixStack.pop();
        
        matrixStack.pop();
        
        // Draw right arm hierarchy
        matrixStack.push();
        if (currentAnimation == AnimationState::WALKING) {
            float armSwing = -getWalkAnimation(animationTime) * 0.5f; // Opposite of left arm
            matrixStack.rotateX(armSwing);
        }
        drawBodyPart(BodyPart::RIGHT_UPPER_ARM, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        
        // Right forearm (child of right upper arm)
        matrixStack.push();
        if (currentAnimation == AnimationState::WALKING) {
            float forearmBend = std::abs(getWalkAnimation(animationTime * 2.0f)) * 0.3f;
            matrixStack.rotateX(-forearmBend);
        }
        drawBodyPart(BodyPart::RIGHT_FOREARM, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        matrixStack.pop();
        
        matrixStack.pop();
        
        // Draw left leg hierarchy
        matrixStack.push();
        if (currentAnimation == AnimationState::WALKING) {
            float legSwing = getWalkAnimation(animationTime) * 0.4f;
            matrixStack.rotateX(legSwing);
        }
        drawBodyPart(BodyPart::LEFT_THIGH, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        
        // Left lower leg (child of left thigh)
        matrixStack.push();
        if (currentAnimation == AnimationState::WALKING) {
            float lowerLegBend = std::max(0.0f, getWalkAnimation(animationTime * 2.0f)) * 0.6f;
            matrixStack.rotateX(lowerLegBend);
        }
        drawBodyPart(BodyPart::LEFT_LOWER_LEG, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        matrixStack.pop();
        
        matrixStack.pop();
        
        // Draw right leg hierarchy
        matrixStack.push();
        if (currentAnimation == AnimationState::WALKING) {
            float legSwing = -getWalkAnimation(animationTime) * 0.4f; // Opposite of left leg
            matrixStack.rotateX(legSwing);
        }
        drawBodyPart(BodyPart::RIGHT_THIGH, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        
        // Right lower leg (child of right thigh)
        matrixStack.push();
        if (currentAnimation == AnimationState::WALKING) {
            float lowerLegBend = std::max(0.0f, -getWalkAnimation(animationTime * 2.0f)) * 0.6f;
            matrixStack.rotateX(lowerLegBend);
        }
        drawBodyPart(BodyPart::RIGHT_LOWER_LEG, matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
        matrixStack.pop();
        
        matrixStack.pop();
        
        matrixStack.pop(); // Restore torso matrix
        matrixStack.pop(); // Restore original matrix
    }
};

#endif // HUMAN_MODEL_H
