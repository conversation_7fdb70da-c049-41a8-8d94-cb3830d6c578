# HumanGL - Hierarchical Modeling Project

A 3D hierarchical human model implementation using C++14, SDL2, and OpenGL 4.0.

## Features

- **Hierarchical Body Structure**: Complete human model with proper parent-child relationships
  - Head (child of torso)
  - <PERSON><PERSON> (root of hierarchy)
  - Arms: Upper arm → Forearm
  - Legs: Thigh → Lower leg

- **Matrix Stack Implementation**: Custom matrix stack for hierarchical transformations

- **Three Animation States**:
  - **Idle**: Static pose
  - **Walking**: Coordinated arm and leg movement with proper swing
  - **Jumping**: Vertical bouncing motion

- **Custom Math Library**: 
  - Vec3, Vec4, Mat4 classes
  - Transformation matrices (translate, rotate, scale)
  - Perspective projection and view matrices

- **Modern OpenGL Rendering**:
  - Vertex and fragment shaders
  - Phong lighting model
  - 1x1x1 cube primitive rendered at origin for each body part

## Requirements

- macOS with Homebrew (or Linux/Windows with appropriate package manager)
- SDL2 development libraries
- OpenGL 4.0+ support
- C++14 compatible compiler

## Installation

1. Install SDL2:
   ```bash
   brew install sdl2  # macOS
   ```

2. Clone and build:
   ```bash
   git clone <your-repo>
   cd humangl
   make
   ```

## Usage

Run the application:
```bash
./human_gl
```

### Controls

- **W**: Start walking animation
- **J**: Start jumping animation  
- **S**: Stop/return to idle pose
- **ESC**: Exit application

## Project Structure

```
humangl/
├── src/
│   └── main.cpp           # Main application loop
├── include/
│   ├── math_utils.h       # Custom math library (Vec3, Mat4, etc.)
│   ├── matrix_stack.h     # Matrix stack for hierarchical transforms
│   ├── shader.h           # Shader loading and management
│   ├── cube_renderer.h    # 1x1x1 cube rendering
│   └── human_model.h      # Hierarchical human model
├── shaders/
│   ├── vertex.glsl        # Vertex shader
│   └── fragment.glsl      # Fragment shader
├── Makefile              # Build system
└── README.md            # This file
```

## Technical Details

### Hierarchical Structure

The human model follows a strict hierarchy where transformations propagate from parent to child:

```
Torso (root)
├── Head
├── Left Upper Arm
│   └── Left Forearm
├── Right Upper Arm
│   └── Right Forearm
├── Left Thigh
│   └── Left Lower Leg
└── Right Thigh
    └── Right Lower Leg
```

### Matrix Stack Operations

Each body part is rendered using the matrix stack pattern:
1. `push()` - Save current transformation
2. Apply local transformations (translate, rotate, scale)
3. Render the 1x1x1 cube
4. Render children (recursively)
5. `pop()` - Restore parent transformation

### Animation System

- **Walking**: Sinusoidal arm and leg swinging with opposite phase
- **Jumping**: Parabolic vertical motion with 2-second cycle
- **Idle**: Static pose with no movement

## Compliance with Requirements

✅ **Hierarchical modeling**: Complete parent-child body structure  
✅ **Matrix stack**: Custom implementation with push/pop operations  
✅ **OpenGL 4.0+**: Modern shader-based rendering  
✅ **C++14**: Standard compliance  
✅ **Custom matrices**: No external math libraries  
✅ **1x1x1 cube function**: Single function draws cube at origin  
✅ **Body parts**: Head, torso, arms (upper/forearm), legs (thigh/lower)  
✅ **Animations**: Walk, jump, and idle states  
✅ **Makefile**: Complete build system  

## Building and Testing

```bash
# Clean build
make clean && make

# Debug build
make debug

# Run
./human_gl

# Test animations
# Press W for walking, J for jumping, S for idle
```

## Troubleshooting

- **Shader compilation errors**: Check that `shaders/` directory exists with `.glsl` files
- **SDL2 not found**: Install SDL2 development libraries
- **OpenGL errors**: Ensure graphics drivers support OpenGL 4.0+
- **Compilation warnings**: Some SDL2 header warnings are normal and can be ignored

## Future Enhancements

- Additional body parts (fingers, toes, neck)
- More complex animations (dancing, martial arts)
- Interactive GUI for real-time parameter adjustment
- Texture mapping and improved materials
- Skeletal animation system with keyframes
