#define GL_GLEXT_PROTOTYPES
#include <SDL2/SDL.h>
#include <SDL2/SDL_opengl.h>
#include <iostream>
#include <cstdlib>
#include "math_utils.h"
#include "matrix_stack.h"
#include "shader.h"
#include "cube_renderer.h"
#include "simple_head.h"

// Window dimensions
const int WINDOW_WIDTH = 800;
const int WINDOW_HEIGHT = 600;

class HumanGL {
private:
    SDL_Window* window;
    SDL_GLContext glContext;
    bool running;

    // Camera and projection matrices
    Mat4 projectionMatrix;
    Mat4 viewMatrix;
    MatrixStack matrixStack;

    // Rendering components
    Shader shader;
    CubeRenderer cubeRenderer;
    SimpleHead simpleHead;

    // Timing
    Uint32 lastTime;

public:
    HumanGL() : window(nullptr), glContext(nullptr), running(false) {}
    
    ~HumanGL() {
        cleanup();
    }
    
    bool initialize() {
        // Initialize SDL
        if (SDL_Init(SDL_INIT_VIDEO) < 0) {
            std::cerr << "SDL could not initialize! SDL Error: " << SDL_GetError() << std::endl;
            return false;
        }
        
        // Set OpenGL attributes
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 4);
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_CORE);
        SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
        SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);
        
        // Create window
        window = SDL_CreateWindow("HumanGL - Hierarchical Modeling",
                                SDL_WINDOWPOS_UNDEFINED, SDL_WINDOWPOS_UNDEFINED,
                                WINDOW_WIDTH, WINDOW_HEIGHT,
                                SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);
        
        if (window == nullptr) {
            std::cerr << "Window could not be created! SDL Error: " << SDL_GetError() << std::endl;
            return false;
        }
        
        // Create OpenGL context
        glContext = SDL_GL_CreateContext(window);
        if (glContext == nullptr) {
            std::cerr << "OpenGL context could not be created! SDL Error: " << SDL_GetError() << std::endl;
            return false;
        }
        
        // Enable VSync
        if (SDL_GL_SetSwapInterval(1) < 0) {
            std::cerr << "Warning: Unable to set VSync! SDL Error: " << SDL_GetError() << std::endl;
        }
        
        // Initialize OpenGL
        if (!initializeOpenGL()) {
            return false;
        }

        // Initialize shaders
        if (!shader.loadFromFiles("shaders/vertex.glsl", "shaders/fragment.glsl")) {
            std::cerr << "Failed to load shaders!" << std::endl;
            return false;
        }

        // Initialize cube renderer
        if (!cubeRenderer.initialize()) {
            std::cerr << "Failed to initialize cube renderer!" << std::endl;
            return false;
        }

        // Initialize timing
        lastTime = SDL_GetTicks();

        running = true;
        return true;
    }
    
    bool initializeOpenGL() {
        // Enable depth testing
        glEnable(GL_DEPTH_TEST);
        glDepthFunc(GL_LESS);

        // Enable backface culling
        glEnable(GL_CULL_FACE);
        glCullFace(GL_BACK);
        glFrontFace(GL_CCW);

        // Set clear color (dark blue)
        glClearColor(0.1f, 0.1f, 0.2f, 1.0f);

        // Set viewport
        glViewport(0, 0, WINDOW_WIDTH, WINDOW_HEIGHT);

        // Setup projection matrix
        float aspect = static_cast<float>(WINDOW_WIDTH) / static_cast<float>(WINDOW_HEIGHT);
        projectionMatrix = Mat4::perspective(toRadians(45.0f), aspect, 0.1f, 100.0f);

        // Setup view matrix (camera looking at origin from a distance)
        Vec3 eye(0.0f, 1.0f, 8.0f);  // Move camera further back and lower
        Vec3 center(0.0f, 0.0f, 0.0f);
        Vec3 up(0.0f, 1.0f, 0.0f);
        viewMatrix = Mat4::lookAt(eye, center, up);

        // Check for OpenGL errors
        GLenum error = glGetError();
        if (error != GL_NO_ERROR) {
            std::cerr << "Error initializing OpenGL! " << error << std::endl;
            return false;
        }

        return true;
    }
    
    void run() {
        SDL_Event e;
        
        while (running) {
            // Handle events
            while (SDL_PollEvent(&e) != 0) {
                if (e.type == SDL_QUIT) {
                    running = false;
                }
                handleInput(e);
            }
            
            // Update
            update();
            
            // Render
            render();
            
            // Swap buffers
            SDL_GL_SwapWindow(window);
        }
    }
    
    void handleInput(const SDL_Event& e) {
        if (e.type == SDL_KEYDOWN) {
            switch (e.key.keysym.sym) {
                case SDLK_ESCAPE:
                    running = false;
                    break;
                case SDLK_r:
                    std::cout << "Rotate head" << std::endl;
                    simpleHead.setAnimation(AnimationState::ROTATING);
                    break;
                case SDLK_s:
                    std::cout << "Stop rotation" << std::endl;
                    simpleHead.setAnimation(AnimationState::IDLE);
                    break;
                default:
                    break;
            }
        }
    }
    
    void update() {
        // Calculate delta time
        Uint32 currentTime = SDL_GetTicks();
        float deltaTime = (currentTime - lastTime) / 1000.0f;
        lastTime = currentTime;

        // Update simple head
        simpleHead.update(deltaTime);
    }
    
    void render() {
        // Clear the screen
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // Use our shader
        shader.use();

        // Set up lighting
        Vec3 lightPos(3.0f, 4.0f, 6.0f);  // Higher and further for better illumination
        Vec3 lightColor(1.0f, 1.0f, 1.0f);
        Vec3 viewPos(0.0f, 1.0f, 8.0f);   // Match camera position

        shader.setVec3("lightPosition", lightPos);
        shader.setVec3("lightColor", lightColor);
        shader.setVec3("viewPosition", viewPos);

        // Reset matrix stack
        matrixStack.loadIdentity();

        // Render the simple head
        simpleHead.render(matrixStack, shader, cubeRenderer, projectionMatrix, viewMatrix);
    }
    
    void cleanup() {
        if (glContext) {
            SDL_GL_DeleteContext(glContext);
            glContext = nullptr;
        }
        
        if (window) {
            SDL_DestroyWindow(window);
            window = nullptr;
        }
        
        SDL_Quit();
    }
};

int main(int argc, char* argv[]) {
    (void)argc;
    (void)argv;
    
    HumanGL app;
    
    if (!app.initialize()) {
        std::cerr << "Failed to initialize application!" << std::endl;
        return EXIT_FAILURE;
    }
    
    app.run();
    
    return EXIT_SUCCESS;
}
