#version 400 core

layout(location = 0) in vec3 position;
layout(location = 1) in vec3 normal;

uniform mat4 mvpMatrix;
uniform mat4 modelMatrix;
uniform mat4 normalMatrix;

out vec3 fragNormal;
out vec3 fragPosition;

void main() {
    // Transform position to clip space
    gl_Position = mvpMatrix * vec4(position, 1.0);
    
    // Transform normal to world space
    fragNormal = normalize((normalMatrix * vec4(normal, 0.0)).xyz);
    
    // Transform position to world space
    fragPosition = (modelMatrix * vec4(position, 1.0)).xyz;
}
